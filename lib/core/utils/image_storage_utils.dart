import 'dart:io';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:storetrack_app/core/utils/logger.dart';

/// Utility class for handling image storage operations
class ImageStorageUtils {
  // Private constructor to prevent instantiation
  ImageStorageUtils._();

  /// Creates a unique filename for an image
  static String _generateUniqueFileName(String extension) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecond;
    return 'img_${timestamp}_$random.$extension';
  }

  /// Gets the app's documents directory for storing images
  static Future<Directory> _getImagesDirectory() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final imagesDir = Directory(path.join(appDocDir.path, 'images'));
    
    // Create the directory if it doesn't exist
    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }
    
    return imagesDir;
  }

  /// Saves an image file to the app's storage and returns the new path
  static Future<String?> saveImageToAppStorage(File imageFile) async {
    try {
      final imagesDir = await _getImagesDirectory();
      final extension = path.extension(imageFile.path).toLowerCase();
      final fileName = _generateUniqueFileName(extension.isEmpty ? 'jpg' : extension.substring(1));
      final newPath = path.join(imagesDir.path, fileName);
      
      // Copy the file to the new location
      final savedFile = await imageFile.copy(newPath);
      
      logger('Image saved to: ${savedFile.path}');
      return savedFile.path;
    } catch (e) {
      logger('Error saving image to app storage: $e');
      return null;
    }
  }

  /// Deletes an image file from storage
  static Future<bool> deleteImage(String imagePath) async {
    try {
      final file = File(imagePath);
      if (await file.exists()) {
        await file.delete();
        logger('Image deleted: $imagePath');
        return true;
      }
      return false;
    } catch (e) {
      logger('Error deleting image: $e');
      return false;
    }
  }

  /// Checks if a file path is a local file (not a network URL)
  static bool isLocalFile(String path) {
    return !path.startsWith('http://') && !path.startsWith('https://');
  }

  /// Gets the file size of an image
  static Future<int?> getImageFileSize(String imagePath) async {
    try {
      if (isLocalFile(imagePath)) {
        final file = File(imagePath);
        if (await file.exists()) {
          return await file.length();
        }
      }
      return null;
    } catch (e) {
      logger('Error getting image file size: $e');
      return null;
    }
  }

  /// Cleans up old images (optional utility for maintenance)
  static Future<void> cleanupOldImages({int maxAgeInDays = 30}) async {
    try {
      final imagesDir = await _getImagesDirectory();
      final cutoffDate = DateTime.now().subtract(Duration(days: maxAgeInDays));
      
      await for (final entity in imagesDir.list()) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await entity.delete();
            logger('Cleaned up old image: ${entity.path}');
          }
        }
      }
    } catch (e) {
      logger('Error cleaning up old images: $e');
    }
  }

  /// Gets all images in the app storage directory
  static Future<List<String>> getAllStoredImages() async {
    try {
      final imagesDir = await _getImagesDirectory();
      final imageFiles = <String>[];
      
      await for (final entity in imagesDir.list()) {
        if (entity is File) {
          final extension = path.extension(entity.path).toLowerCase();
          if (['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'].contains(extension)) {
            imageFiles.add(entity.path);
          }
        }
      }
      
      return imageFiles;
    } catch (e) {
      logger('Error getting stored images: $e');
      return [];
    }
  }
}
