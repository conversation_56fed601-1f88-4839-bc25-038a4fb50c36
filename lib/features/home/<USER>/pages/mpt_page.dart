import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';
import 'package:storetrack_app/core/services/camera_service.dart';
import 'package:storetrack_app/core/utils/image_storage_utils.dart';
import 'package:storetrack_app/core/utils/snackbar_service.dart';
import 'package:storetrack_app/di/service_locator.dart';
import 'package:storetrack_app/shared/widgets/custom_app_bar.dart';
import 'package:storetrack_app/features/auth/presentation/widgets/app_button.dart';

// View modes enum
enum ViewMode { stack, list, edit }

@RoutePage()
class MPTPage extends StatefulWidget {
  final String? taskId;
  final String? formId;
  final String? questionId;
  final String? questionPartId;
  final String? measurementId;
  final String? combineTypeId;
  final String? questionPartMultiId;

  const MPTPage({
    super.key,
    this.taskId,
    this.formId,
    this.questionId,
    this.questionPartId,
    this.measurementId,
    this.combineTypeId,
    this.questionPartMultiId,
  });

  @override
  State<MPTPage> createState() => _MPTPageState();
}

class _MPTPageState extends State<MPTPage> {
  // Current view mode
  ViewMode _currentMode = ViewMode.stack;

  // Camera service instance
  late final CameraService _cameraService;

  // Sample image data - replace with actual data
  final List<Map<String, String>> _images = [
    {
      'url':
          'https://plus.unsplash.com/premium_photo-1669138140804-a593bab110d2?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'description': '"The display looked fine and undamaged"'
    },
    {
      'url':
          'https://plus.unsplash.com/premium_photo-1664464228938-0fcef7296c95?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'description': '"Not the best location as quite hidden"'
    },
    {
      'url':
          'https://images.unsplash.com/photo-1489710437720-ebb67ec84dd2?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      'description': '"New location was here"'
    },
  ];

  // Text controllers for edit mode
  late List<TextEditingController> _textControllers;

  @override
  void initState() {
    super.initState();
    _cameraService = sl<CameraService>();
    _textControllers = _images
        .map((image) => TextEditingController(text: image['description']))
        .toList();
  }

  @override
  void dispose() {
    for (var controller in _textControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.lightGrey2,
      appBar: CustomAppBar(
        title: 'Photos',
        actions: [
          _buildAppBarButton(
            icon: Icons.grid_view,
            isSelected: _currentMode == ViewMode.stack,
            onTap: () => _setViewMode(ViewMode.stack),
          ),
          const Gap(8),
          _buildAppBarButton(
            icon: Icons.view_list,
            isSelected: _currentMode == ViewMode.list,
            onTap: () => _setViewMode(ViewMode.list),
          ),
          const Gap(8),
          _buildAppBarButton(
            icon: Icons.camera_alt,
            isSelected: false,
            onTap: _onCameraTap,
          ),
          const Gap(8),
          _buildAppBarButton(
            icon: Icons.edit,
            isSelected: _currentMode == ViewMode.edit,
            onTap: () => _setViewMode(ViewMode.edit),
          ),
          const Gap(16),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildAppBarButton({
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          // color: isSelected ? AppColors.primaryBlue : Colors.transparent,
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          size: 18,
          color: isSelected ? AppColors.primaryBlue : AppColors.blackTint1,
        ),
      ),
    );
  }

  void _setViewMode(ViewMode mode) {
    setState(() {
      _currentMode = mode;
    });
  }

  void _onCameraTap() {
    _showImageSourceDialog();
  }

  /// Shows dialog to choose between camera and gallery
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackTint2,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Gap(16),
                Text(
                  'Add Photo',
                  style: Theme.of(context).textTheme.montserratTitleSmall,
                ),
                const Gap(24),
                // Camera option
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: AppColors.primaryBlue,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'Take Photo',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  subtitle: Text(
                    'Use camera to capture a new photo',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.blackTint1,
                        ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _captureImage(ImageSource.camera);
                  },
                ),
                // Gallery option
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.photo_library,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'Choose from Gallery',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  subtitle: Text(
                    'Select an existing photo from gallery',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.blackTint1,
                        ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _captureImage(ImageSource.gallery);
                  },
                ),
                const Gap(16),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Captures image from camera or gallery
  Future<void> _captureImage(ImageSource source) async {
    try {
      final File? imageFile = await _cameraService.pickImage(source: source);

      if (imageFile != null) {
        // Save the image to app storage
        final String? savedImagePath =
            await ImageStorageUtils.saveImageToAppStorage(imageFile);

        if (savedImagePath != null) {
          // Add the new image to the list
          setState(() {
            _images.add({
              'url': savedImagePath,
              'description': '"New photo captured"',
            });

            // Add a new text controller for the new image
            _textControllers.add(
              TextEditingController(text: '"New photo captured"'),
            );
          });

          if (mounted) {
            SnackBarService.success(
              context: context,
              message: 'Photo added successfully!',
            );
          }
        } else {
          if (mounted) {
            SnackBarService.error(
              context: context,
              message: 'Failed to save photo. Please try again.',
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to capture photo: ${e.toString()}',
        );
      }
    }
  }

  Widget _buildBody() {
    switch (_currentMode) {
      case ViewMode.stack:
        return _buildStackView();
      case ViewMode.list:
        return _buildListView();
      case ViewMode.edit:
        return _buildEditView();
    }
  }

  Widget _buildStackView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView.separated(
        itemCount: _images.length,
        separatorBuilder: (context, index) => const Gap(16),
        itemBuilder: (context, index) {
          final image = _images[index];
          return _buildStackImageCard(image);
        },
      ),
    );
  }

  Widget _buildStackImageCard(Map<String, String> image) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Square image taking full width
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: AspectRatio(
              aspectRatio: 1.0,
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                child: _buildImageWidget(image['url']!),
              ),
            ),
          ),
          // Description
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              image['description']!,
              style: Theme.of(context).textTheme.montserratParagraphSmall,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView.separated(
        itemCount: _images.length,
        separatorBuilder: (context, index) => const Gap(12),
        itemBuilder: (context, index) {
          final image = _images[index];
          return _buildListImageCard(image);
        },
      ),
    );
  }

  Widget _buildListImageCard(Map<String, String> image) {
    return Container(
      height: 120, // Card height is half of width (approximately)
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Square image on the left
          AspectRatio(
            aspectRatio: 1.0,
            child: Container(
              width: 120,
              height: 120,
              decoration: const BoxDecoration(
                borderRadius:
                    BorderRadius.horizontal(left: Radius.circular(12)),
              ),
              margin: const EdgeInsets.all(8.0),
              child: ClipRRect(
                borderRadius: const BorderRadius.all(Radius.circular(12)),
                child: _buildImageWidget(image['url']!),
              ),
            ),
          ),
          // Text on the right, aligned to top
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Align(
                alignment: Alignment.topLeft,
                child: Text(
                  image['description']!,
                  style: Theme.of(context).textTheme.montserratParagraphSmall,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView.separated(
        itemCount: _images.length,
        separatorBuilder: (context, index) => const Gap(16),
        itemBuilder: (context, index) {
          final image = _images[index];
          return _buildEditImageCard(image, index);
        },
      ),
    );
  }

  Widget _buildEditImageCard(Map<String, String> image, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Square image with reduced width (similar to StoreCard edit mode)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // Image with reduced width
                Expanded(
                  flex: 3,
                  child: AspectRatio(
                    aspectRatio: 1.0,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: _buildImageWidget(image['url']!),
                    ),
                  ),
                ),
                const Gap(16),
                // Action buttons in vertical layout (similar to StoreCard)
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      _buildEditActionButton(
                        icon: Icons.delete_outline,
                        onTap: () => _onDeleteImage(index),
                        color: Colors.red,
                      ),
                      const Gap(8),
                      _buildEditActionButton(
                        icon: Icons.edit,
                        onTap: () => _onEditImage(index),
                        color: AppColors.primaryBlue,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Editable text area
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 0, 16.0, 16.0),
            child: TextField(
              controller: _textControllers[index],
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Enter description...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.blackTint2),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: AppColors.primaryBlue),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
              style: Theme.of(context).textTheme.montserratParagraphSmall,
              onChanged: (value) {
                // Update the image description
                _images[index]['description'] = value;
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Builds image widget that handles both local files and network URLs
  Widget _buildImageWidget(String imagePath) {
    if (ImageStorageUtils.isLocalFile(imagePath)) {
      // Local file
      return Image.file(
        File(imagePath),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: AppColors.lightGrey2,
            child: const Icon(
              Icons.broken_image,
              color: AppColors.blackTint1,
              size: 48,
            ),
          );
        },
      );
    } else {
      // Network URL
      return Image.network(
        imagePath,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: AppColors.lightGrey2,
            child: const Icon(
              Icons.broken_image,
              color: AppColors.blackTint1,
              size: 48,
            ),
          );
        },
      );
    }
  }

  Widget _buildEditActionButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color,
            width: 1,
          ),
        ),
        child: Icon(
          icon,
          color: color,
          size: 20,
        ),
      ),
    );
  }

  void _onDeleteImage(int index) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Image',
            style: Theme.of(context).textTheme.montserratTitleSmall,
          ),
          content: Text(
            'Are you sure you want to delete this image?',
            style: Theme.of(context).textTheme.montserratParagraphSmall,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: Theme.of(context)
                    .textTheme
                    .montserratParagraphSmall
                    .copyWith(
                      color: AppColors.blackTint1,
                    ),
              ),
            ),
            SizedBox(
              width: 80,
              height: 36,
              child: AppButton(
                text: 'Delete',
                color: Colors.red,
                onPressed: () async {
                  final navigator = Navigator.of(context);

                  // Delete the image file if it's a local file
                  final imagePath = _images[index]['url']!;
                  if (ImageStorageUtils.isLocalFile(imagePath)) {
                    await ImageStorageUtils.deleteImage(imagePath);
                  }

                  setState(() {
                    _images.removeAt(index);
                    _textControllers[index].dispose();
                    _textControllers.removeAt(index);
                  });
                  navigator.pop();
                },
                height: 36,
                textColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  void _onEditImage(int index) {
    _showReplaceImageDialog(index);
  }

  /// Shows dialog to replace an existing image
  void _showReplaceImageDialog(int index) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.blackTint2,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const Gap(16),
                Text(
                  'Replace Photo',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Gap(24),
                // Camera option
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.camera_alt,
                      color: AppColors.primaryBlue,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'Take New Photo',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  subtitle: Text(
                    'Use camera to capture a replacement photo',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.blackTint1,
                        ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _replaceImage(index, ImageSource.camera);
                  },
                ),
                // Gallery option
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.photo_library,
                      color: Colors.green,
                      size: 20,
                    ),
                  ),
                  title: Text(
                    'Choose from Gallery',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                  subtitle: Text(
                    'Select a replacement photo from gallery',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.blackTint1,
                        ),
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    _replaceImage(index, ImageSource.gallery);
                  },
                ),
                const Gap(16),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Replaces an existing image at the given index
  Future<void> _replaceImage(int index, ImageSource source) async {
    try {
      final File? imageFile = await _cameraService.pickImage(source: source);

      if (imageFile != null) {
        // Save the new image to app storage
        final String? savedImagePath =
            await ImageStorageUtils.saveImageToAppStorage(imageFile);

        if (savedImagePath != null) {
          // Delete the old image if it's a local file
          final oldImagePath = _images[index]['url']!;
          if (ImageStorageUtils.isLocalFile(oldImagePath)) {
            await ImageStorageUtils.deleteImage(oldImagePath);
          }

          // Update the image in the list
          setState(() {
            _images[index]['url'] = savedImagePath;
          });

          if (mounted) {
            SnackBarService.success(
              context: context,
              message: 'Photo replaced successfully!',
            );
          }
        } else {
          if (mounted) {
            SnackBarService.error(
              context: context,
              message: 'Failed to save replacement photo. Please try again.',
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        SnackBarService.error(
          context: context,
          message: 'Failed to replace photo: ${e.toString()}',
        );
      }
    }
  }
}
